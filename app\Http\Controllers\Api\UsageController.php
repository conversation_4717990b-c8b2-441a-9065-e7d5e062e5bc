<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UsageController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $currentPlan = $user->currentPlan;

        if (!$currentPlan) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'NO_ACTIVE_PLAN',
                    'message' => 'No active plan found',
                ],
            ], 404);
        }

        // Get usage statistics for this month
        $startOfMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        $documentsThisMonth = $user->documents()
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->get();

        $usageByType = $documentsThisMonth->groupBy('documentType.slug')
            ->map(function ($docs) {
                return $docs->count();
            });

        $successRate = $documentsThisMonth->count() > 0
            ? $documentsThisMonth->where('status', 'completed')->count() / $documentsThisMonth->count()
            : 0;

        return response()->json([
            'success' => true,
            'data' => [
                'current_plan' => [
                    'name' => $currentPlan->plan->name,
                    'monthly_credits' => $currentPlan->plan->monthly_credits,
                    'credits_remaining' => $user->remaining_credits,
                    'credits_used_this_month' => $currentPlan->plan->monthly_credits - $user->remaining_credits,
                    'expires_at' => $currentPlan->expires_at ? $currentPlan->expires_at->format('Y-m-d') : null,
                ],
                'usage_this_month' => [
                    'total_documents' => $documentsThisMonth->count(),
                    'by_type' => $usageByType,
                    'success_rate' => round($successRate, 2),
                ],
            ],
        ]);
    }

    public function history(Request $request)
    {
        $request->validate([
            'from' => 'nullable|date',
            'to' => 'nullable|date|after_or_equal:from',
            'group_by' => 'nullable|string|in:day,week,month',
        ]);

        $user = $request->user();
        $from = $request->from ? \Carbon\Carbon::parse($request->from) : now()->subMonth();
        $to = $request->to ? \Carbon\Carbon::parse($request->to) : now();
        $groupBy = $request->group_by ?? 'day';

        $dateFormat = match($groupBy) {
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            default => '%Y-%m-%d',
        };

        $usage = DB::table('documents')
            ->where('user_id', $user->id)
            ->whereBetween('created_at', [$from, $to])
            ->select([
                DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as period"),
                DB::raw('COUNT(*) as documents_processed'),
                DB::raw('SUM(credits_used) as credits_used'),
                DB::raw('AVG(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as success_rate'),
            ])
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'period' => [
                    'from' => $from->format('Y-m-d'),
                    'to' => $to->format('Y-m-d'),
                    'group_by' => $groupBy,
                ],
                'usage' => $usage->map(function ($item) use ($groupBy) {
                    return [
                        'date' => $item->period,
                        'documents_processed' => (int) $item->documents_processed,
                        'credits_used' => (int) $item->credits_used,
                        'success_rate' => round((float) $item->success_rate, 2),
                    ];
                }),
            ],
        ]);
    }
}
